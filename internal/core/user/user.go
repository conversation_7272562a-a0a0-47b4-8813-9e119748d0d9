
package user

import (
	"golang-light/internal/model"

	"gorm.io/gorm"
)

// UserStore defines the interface for user data access
type UserStore interface {
	Create(user *model.User) error
	GetByUsername(username string) (*model.User, error)
}

// userStore is the implementation of UserStore
type userStore struct {
	DB *gorm.DB
}

// NewUserStore creates a new UserStore
func NewUserStore(db *gorm.DB) UserStore {
	return &userStore{DB: db}
}

// C<PERSON> creates a new user
func (s *userStore) Create(user *model.User) error {
	return s.DB.Create(user).Error
}

// GetByUsername gets a user by username
func (s *userStore) GetByUsername(username string) (*model.User, error) {
	var user model.User
	if err := s.DB.Where("username = ?", username).First(&user).Error; err != nil {
		return nil, err
	}
	return &user, nil
}

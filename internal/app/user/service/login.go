
package service

import (
	"errors"
	"golang-light/internal/core/user"
)

// LoginService provides user login functionality
type LoginService struct {
	userStore user.UserStore
}

// NewLoginService creates a new LoginService
func NewLoginService(userStore user.UserStore) *LoginService {
	return &LoginService{userStore: userStore}
}

// Login logs a user in
func (s *LoginService) Login(username, password string) (string, error) {
	user, err := s.userStore.GetByUsername(username)
	if err != nil {
		return "", err
	}

	// In a real application, you should compare hashed passwords
	if user.Password != password {
		return "", errors.New("invalid credentials")
	}

	// In a real application, you would return a JWT token here
	return "login_successful_token", nil
}

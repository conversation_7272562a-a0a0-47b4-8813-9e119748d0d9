
package service

import (
	"golang-light/internal/core/user"
	"golang-light/internal/model"
)

// RegisterService provides user registration functionality
type RegisterService struct {
	userStore user.UserStore
}

// NewRegisterService creates a new RegisterService
func NewRegisterService(userStore user.UserStore) *RegisterService {
	return &RegisterService{userStore: userStore}
}

// Register registers a new user
func (s *RegisterService) Register(username, password string) error {
	// In a real application, you should hash the password
	user := &model.User{
		Username: username,
		Password: password, // Remember to hash passwords in production!
	}
	return s.userStore.Create(user)
}

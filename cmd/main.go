
package main

import (
	"fmt"
	"log"

	"golang-light/internal/app/user"
	appsvc "golang-light/internal/app/user/service"
	"golang-light/internal/config"
	"golang-light/internal/core/user"
	"golang-light/internal/model"

	"github.com/gin-gonic/gin"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

func main() {
	// Load configuration
	cfg, err := config.LoadConfig(".")
	if err != nil {
		log.Fatalf("could not load config: %v", err)
	}

	// Initialize database
	db, err := gorm.Open(mysql.Open(cfg.Database.Source), &gorm.Config{})
	if err != nil {
		log.Fatalf("could not connect to database: %v", err)
	}

	// Auto-migrate the schema
	db.AutoMigrate(&model.User{})

	// Initialize dependencies
	userStore := usercore.NewUserStore(db)
	registerSvc := appsvc.NewRegisterService(userStore)
	loginSvc := appsvc.NewLoginService(userStore)
	userController := appuser.NewUserController(loginSvc, registerSvc)

	// Initialize Gin engine
	gin.SetMode(cfg.Server.Mode)
	r := gin.Default()

	// Register routes
	appuser.RegisterRoutes(r, userController)

	// Start server
	addr := fmt.Sprintf(":%s", cfg.Server.Port)
	if err := r.Run(addr); err != nil {
		log.Fatalf("could not start server: %v", err)
	}
}
